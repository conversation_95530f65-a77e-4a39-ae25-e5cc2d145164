{"name": "aquot-portifolio-backend", "version": "1.0.0", "description": "Backend for aquot portifolio", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js"}, "dependencies": {"@prisma/client": "^6.14.0", "axios": "^1.11.0", "bcrypt": "^6.0.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.6.1", "ejs": "^3.1.9", "express": "^4.21.2", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "joi": "^18.0.0", "jsonwebtoken": "^9.0.2", "method-override": "^3.0.0", "mongoose": "^7.5.0", "morgan": "^1.10.1", "multer": "^2.0.2", "nodemailer": "^7.0.5", "pg": "^8.16.3", "prisma": "^6.14.0", "prom-client": "^15.1.3", "sharp": "^0.34.3", "stripe": "^18.4.0", "winston": "^3.17.0"}, "devDependencies": {"eslint": "^9.33.0", "nodemon": "^3.1.10", "prettier": "^3.6.2"}, "engines": {"node": ">=16.0.0"}, "keywords": [], "author": "", "license": "MIT"}