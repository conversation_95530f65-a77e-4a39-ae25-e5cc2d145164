# Node + Express (MVC) Starter with Mongoose

## Features
- MVC folder layout (models, views, controllers, routes)
- MongoDB connection via Mongoose
- EJS templating
- Basic User CRUD (create, read, update, delete)
- Example environment config

## Quick start
1. Install dependencies:
```
npm install
```
2. Copy `.env.example` to `.env` and update `MONGODB_URI` if needed.
3. Start MongoDB locally (or use a cloud URI) and run:
```
npm run dev
```
4. Open `http://localhost:3000`.