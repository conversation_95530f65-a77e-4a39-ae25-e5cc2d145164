require("dotenv").config();
const express = require("express");
const path = require("path");
const methodOverride = require("method-override");
const bodyParser = require("body-parser");
const cors = require("cors");
const helmet = require("helmet");
const morgan = require("morgan");

const app = express();

// connect DB
require("./config/db")();

app.set("view engine", "ejs");
app.set("views", path.join(__dirname, "views"));

// Security middlewares
app.use(helmet());
app.use(cors());
app.use(morgan("combined"));

// Body parsing middlewares
app.use(express.static(path.join(__dirname, "public")));
app.use(bodyParser.urlencoded({ extended: true }));
app.use(bodyParser.json());
app.use(methodOverride("_method"));

// routes
const solutionsRouter = require("./modules/solutions/solutions.routes");

app.use("/api/solutions", solutionsRouter);

// error handler
app.use((req, res) => {
  res.status(404).render("404", { url: req.originalUrl });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () =>
  console.log(`Server running on http://localhost:${PORT}`)
);
