// Simple test script to verify Solutions CRUD operations
// Run this after starting the server with: node test-solutions.js

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/solutions';

// Sample solution data for testing
const sampleSolution = {
  project_name: "E-commerce Platform",
  project_smallDescription: "A modern e-commerce platform for small businesses",
  project_description: "This comprehensive e-commerce platform provides small businesses with all the tools they need to sell online. Features include inventory management, payment processing, order tracking, and customer management.",
  project_image: "https://example.com/images/ecommerce-platform.jpg",
  project_link: "https://example.com/projects/ecommerce-platform",
  project_client: "Small Business Solutions Inc.",
  services_provided: "Web Development",
  timeline: "3 months",
  problem_statement: "Small businesses needed an affordable, easy-to-use e-commerce solution that could compete with larger platforms.",
  problem_images: ["https://example.com/images/problem1.jpg"],
  solution_images: ["https://example.com/images/solution1.jpg", "https://example.com/images/solution2.jpg"]
};

const updatedSolution = {
  project_name: "Advanced E-commerce Platform",
  project_smallDescription: "An advanced e-commerce platform with AI features",
  project_description: "This enhanced e-commerce platform now includes AI-powered recommendations, automated inventory management, and advanced analytics to help small businesses grow their online presence.",
  project_image: "https://example.com/images/advanced-ecommerce-platform.jpg",
  project_link: "https://example.com/projects/advanced-ecommerce-platform",
  project_client: "Small Business Solutions Inc.",
  services_provided: "Web Development",
  timeline: "4 months",
  problem_statement: "Small businesses needed more advanced features to compete in the modern e-commerce landscape.",
  problem_images: ["https://example.com/images/problem1.jpg", "https://example.com/images/problem2.jpg"],
  solution_images: ["https://example.com/images/solution1.jpg", "https://example.com/images/solution2.jpg", "https://example.com/images/solution3.jpg"]
};

async function testCRUDOperations() {
  let createdSolutionId = null;

  try {
    console.log('🚀 Starting Solutions CRUD Tests...\n');

    // Test 1: CREATE - POST /api/solutions
    console.log('1. Testing CREATE operation...');
    try {
      const createResponse = await axios.post(BASE_URL, sampleSolution);
      console.log('✅ CREATE Success:', createResponse.status);
      console.log('   Created solution ID:', createResponse.data.data._id);
      createdSolutionId = createResponse.data.data._id;
    } catch (error) {
      console.log('❌ CREATE Failed:', error.response?.status, error.response?.data?.message);
      return;
    }

    // Test 2: READ ALL - GET /api/solutions
    console.log('\n2. Testing READ ALL operation...');
    try {
      const readAllResponse = await axios.get(BASE_URL);
      console.log('✅ READ ALL Success:', readAllResponse.status);
      console.log('   Total solutions:', readAllResponse.data.data.length);
      console.log('   Pagination info:', readAllResponse.data.pagination);
    } catch (error) {
      console.log('❌ READ ALL Failed:', error.response?.status, error.response?.data?.message);
    }

    // Test 3: READ ONE - GET /api/solutions/:id
    console.log('\n3. Testing READ ONE operation...');
    try {
      const readOneResponse = await axios.get(`${BASE_URL}/${createdSolutionId}`);
      console.log('✅ READ ONE Success:', readOneResponse.status);
      console.log('   Solution name:', readOneResponse.data.data.project_name);
    } catch (error) {
      console.log('❌ READ ONE Failed:', error.response?.status, error.response?.data?.message);
    }

    // Test 4: UPDATE - PUT /api/solutions/:id
    console.log('\n4. Testing UPDATE operation...');
    try {
      const updateResponse = await axios.put(`${BASE_URL}/${createdSolutionId}`, updatedSolution);
      console.log('✅ UPDATE Success:', updateResponse.status);
      console.log('   Updated solution name:', updateResponse.data.data.project_name);
    } catch (error) {
      console.log('❌ UPDATE Failed:', error.response?.status, error.response?.data?.message);
    }

    // Test 5: PARTIAL UPDATE - PATCH /api/solutions/:id
    console.log('\n5. Testing PARTIAL UPDATE operation...');
    try {
      const patchData = { timeline: "5 months" };
      const patchResponse = await axios.patch(`${BASE_URL}/${createdSolutionId}`, patchData);
      console.log('✅ PARTIAL UPDATE Success:', patchResponse.status);
      console.log('   Updated timeline:', patchResponse.data.data.timeline);
    } catch (error) {
      console.log('❌ PARTIAL UPDATE Failed:', error.response?.status, error.response?.data?.message);
    }

    // Test 6: READ with filtering
    console.log('\n6. Testing READ with filtering...');
    try {
      const filterResponse = await axios.get(`${BASE_URL}?services_provided=Web Development&limit=5`);
      console.log('✅ FILTERED READ Success:', filterResponse.status);
      console.log('   Filtered results:', filterResponse.data.data.length);
    } catch (error) {
      console.log('❌ FILTERED READ Failed:', error.response?.status, error.response?.data?.message);
    }

    // Test 7: DELETE - DELETE /api/solutions/:id
    console.log('\n7. Testing DELETE operation...');
    try {
      const deleteResponse = await axios.delete(`${BASE_URL}/${createdSolutionId}`);
      console.log('✅ DELETE Success:', deleteResponse.status);
      console.log('   Deleted solution:', deleteResponse.data.data.project_name);
    } catch (error) {
      console.log('❌ DELETE Failed:', error.response?.status, error.response?.data?.message);
    }

    // Test 8: Verify deletion
    console.log('\n8. Verifying deletion...');
    try {
      await axios.get(`${BASE_URL}/${createdSolutionId}`);
      console.log('❌ Deletion verification failed - solution still exists');
    } catch (error) {
      if (error.response?.status === 404) {
        console.log('✅ Deletion verified - solution not found (expected)');
      } else {
        console.log('❌ Unexpected error during deletion verification:', error.response?.status);
      }
    }

    console.log('\n🎉 All CRUD tests completed!');

  } catch (error) {
    console.error('❌ Test suite failed:', error.message);
  }
}

// Test validation errors
async function testValidationErrors() {
  console.log('\n🔍 Testing validation errors...\n');

  // Test invalid data
  const invalidSolution = {
    project_name: "AB", // Too short
    project_smallDescription: "Short", // Too short
    project_description: "Too short description", // Too short
    project_image: "invalid-url", // Invalid URL
    project_link: "invalid-url", // Invalid URL
    project_client: "A", // Too short
    services_provided: "Invalid Service", // Invalid enum value
    timeline: "AB", // Too short
    problem_statement: "Too short" // Too short
  };

  try {
    await axios.post(BASE_URL, invalidSolution);
    console.log('❌ Validation test failed - invalid data was accepted');
  } catch (error) {
    if (error.response?.status === 400) {
      console.log('✅ Validation working correctly - invalid data rejected');
      console.log('   Validation errors:', error.response.data.errors?.length || 0);
    } else {
      console.log('❌ Unexpected validation error:', error.response?.status);
    }
  }
}

// Run tests
async function runAllTests() {
  console.log('🧪 Solutions API Test Suite');
  console.log('============================\n');
  
  await testCRUDOperations();
  await testValidationErrors();
  
  console.log('\n✨ Test suite completed!');
}

// Check if server is running before starting tests
async function checkServer() {
  try {
    await axios.get('http://localhost:3000/api/solutions');
    return true;
  } catch (error) {
    return false;
  }
}

// Main execution
(async () => {
  const serverRunning = await checkServer();
  
  if (!serverRunning) {
    console.log('❌ Server is not running on http://localhost:3000');
    console.log('   Please start the server with: npm run dev');
    process.exit(1);
  }
  
  await runAllTests();
})();
