# Solutions API Usage Examples

This document provides practical examples of how to use the Solutions API with curl commands and JavaScript fetch.

## Prerequisites

Make sure the server is running:
```bash
npm run dev
```

The API will be available at: `http://localhost:3000/api/solutions`

## Curl Examples

### 1. Create a New Solution

```bash
curl -X POST http://localhost:3000/api/solutions \
  -H "Content-Type: application/json" \
  -d '{
    "project_name": "Mobile Banking App",
    "project_smallDescription": "A secure mobile banking application for modern users",
    "project_description": "This mobile banking app provides users with secure access to their accounts, transaction history, money transfers, and bill payments. Built with React Native and featuring biometric authentication.",
    "project_image": "https://example.com/images/mobile-banking.jpg",
    "project_link": "https://github.com/example/mobile-banking-app",
    "project_client": "First National Bank",
    "services_provided": "Mobile App Development",
    "timeline": "6 months",
    "problem_statement": "The bank needed a modern mobile app to compete with fintech startups and provide better customer experience.",
    "problem_images": ["https://example.com/images/old-app.jpg"],
    "solution_images": ["https://example.com/images/new-app-1.jpg", "https://example.com/images/new-app-2.jpg"]
  }'
```

### 2. Get All Solutions

```bash
# Get all solutions (default pagination)
curl http://localhost:3000/api/solutions

# Get solutions with pagination
curl "http://localhost:3000/api/solutions?page=1&limit=5"

# Get solutions filtered by service
curl "http://localhost:3000/api/solutions?services_provided=Web%20Development"

# Get solutions filtered by client
curl "http://localhost:3000/api/solutions?client=bank"
```

### 3. Get Solution by ID

```bash
curl http://localhost:3000/api/solutions/SOLUTION_ID_HERE
```

### 4. Update Solution (Full Update)

```bash
curl -X PUT http://localhost:3000/api/solutions/SOLUTION_ID_HERE \
  -H "Content-Type: application/json" \
  -d '{
    "project_name": "Advanced Mobile Banking App",
    "project_smallDescription": "An advanced mobile banking app with AI features",
    "project_description": "Enhanced mobile banking app with AI-powered financial insights, automated savings, and advanced security features including facial recognition and fraud detection.",
    "project_image": "https://example.com/images/advanced-mobile-banking.jpg",
    "project_link": "https://github.com/example/advanced-mobile-banking-app",
    "project_client": "First National Bank",
    "services_provided": "Mobile App Development",
    "timeline": "8 months",
    "problem_statement": "The bank wanted to add AI features and enhanced security to stay ahead of competitors.",
    "problem_images": ["https://example.com/images/old-app.jpg"],
    "solution_images": ["https://example.com/images/ai-app-1.jpg", "https://example.com/images/ai-app-2.jpg"]
  }'
```

### 5. Partial Update Solution

```bash
curl -X PATCH http://localhost:3000/api/solutions/SOLUTION_ID_HERE \
  -H "Content-Type: application/json" \
  -d '{
    "timeline": "9 months",
    "project_description": "Updated description with new features"
  }'
```

### 6. Delete Solution

```bash
curl -X DELETE http://localhost:3000/api/solutions/SOLUTION_ID_HERE
```

## JavaScript Fetch Examples

### 1. Create a New Solution

```javascript
const newSolution = {
  project_name: "E-learning Platform",
  project_smallDescription: "Interactive online learning platform for students",
  project_description: "A comprehensive e-learning platform with video courses, interactive quizzes, progress tracking, and certification system. Built with React and Node.js.",
  project_image: "https://example.com/images/elearning.jpg",
  project_link: "https://example.com/projects/elearning-platform",
  project_client: "Education First Academy",
  services_provided: "Web Development",
  timeline: "4 months",
  problem_statement: "The academy needed a modern online platform to deliver courses remotely during the pandemic.",
  problem_images: ["https://example.com/images/old-classroom.jpg"],
  solution_images: ["https://example.com/images/platform-1.jpg", "https://example.com/images/platform-2.jpg"]
};

fetch('http://localhost:3000/api/solutions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(newSolution)
})
.then(response => response.json())
.then(data => {
  console.log('Solution created:', data);
})
.catch(error => {
  console.error('Error:', error);
});
```

### 2. Get All Solutions with Pagination

```javascript
async function getSolutions(page = 1, limit = 10) {
  try {
    const response = await fetch(`http://localhost:3000/api/solutions?page=${page}&limit=${limit}`);
    const data = await response.json();
    
    if (data.success) {
      console.log('Solutions:', data.data);
      console.log('Pagination:', data.pagination);
      return data;
    } else {
      console.error('Failed to fetch solutions:', data.message);
    }
  } catch (error) {
    console.error('Error fetching solutions:', error);
  }
}

// Usage
getSolutions(1, 5);
```

### 3. Get Solution by ID

```javascript
async function getSolutionById(id) {
  try {
    const response = await fetch(`http://localhost:3000/api/solutions/${id}`);
    const data = await response.json();
    
    if (data.success) {
      console.log('Solution:', data.data);
      return data.data;
    } else {
      console.error('Solution not found:', data.message);
    }
  } catch (error) {
    console.error('Error fetching solution:', error);
  }
}
```

### 4. Update Solution

```javascript
async function updateSolution(id, updates) {
  try {
    const response = await fetch(`http://localhost:3000/api/solutions/${id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates)
    });
    
    const data = await response.json();
    
    if (data.success) {
      console.log('Solution updated:', data.data);
      return data.data;
    } else {
      console.error('Update failed:', data.message);
    }
  } catch (error) {
    console.error('Error updating solution:', error);
  }
}

// Usage
updateSolution('SOLUTION_ID_HERE', {
  timeline: '5 months',
  project_description: 'Updated description'
});
```

### 5. Delete Solution

```javascript
async function deleteSolution(id) {
  try {
    const response = await fetch(`http://localhost:3000/api/solutions/${id}`, {
      method: 'DELETE'
    });
    
    const data = await response.json();
    
    if (data.success) {
      console.log('Solution deleted:', data.message);
      return true;
    } else {
      console.error('Delete failed:', data.message);
      return false;
    }
  } catch (error) {
    console.error('Error deleting solution:', error);
    return false;
  }
}
```

## Error Handling Examples

### Handle Validation Errors

```javascript
async function createSolutionWithErrorHandling(solutionData) {
  try {
    const response = await fetch('http://localhost:3000/api/solutions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(solutionData)
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('Solution created successfully:', data.data);
      return data.data;
    } else {
      // Handle different error types
      switch (response.status) {
        case 400:
          console.error('Validation errors:');
          data.errors?.forEach(error => {
            console.error(`- ${error.field}: ${error.message}`);
          });
          break;
        case 409:
          console.error('Conflict:', data.message);
          break;
        case 429:
          console.error('Rate limit exceeded:', data.message);
          break;
        default:
          console.error('Unexpected error:', data.message);
      }
      return null;
    }
  } catch (error) {
    console.error('Network error:', error);
    return null;
  }
}
```

## Testing the API

You can use the provided test script to verify all operations:

```bash
node test-solutions.js
```

This will run a comprehensive test suite that verifies:
- Creating solutions
- Reading all solutions with pagination
- Reading individual solutions
- Updating solutions (full and partial)
- Deleting solutions
- Validation error handling
