const Joi = require('joi');

// Base validation schema for solution data
const solutionSchema = Joi.object({
  project_name: Joi.string()
    .required()
    .min(3)
    .max(100)
    .trim()
    .pattern(/^[a-zA-Z0-9\s\-_.,!?()]+$/)
    .messages({
      'string.pattern.base': 'Project name contains invalid characters',
      'string.min': 'Project name must be at least 3 characters long',
      'string.max': 'Project name must not exceed 100 characters',
      'any.required': 'Project name is required'
    }),

  project_smallDescription: Joi.string()
    .required()
    .min(10)
    .max(200)
    .trim()
    .messages({
      'string.min': 'Small description must be at least 10 characters long',
      'string.max': 'Small description must not exceed 200 characters',
      'any.required': 'Small description is required'
    }),

  project_description: Joi.string()
    .required()
    .min(50)
    .max(2000)
    .trim()
    .messages({
      'string.min': 'Project description must be at least 50 characters long',
      'string.max': 'Project description must not exceed 2000 characters',
      'any.required': 'Project description is required'
    }),

  project_image: Joi.string()
    .required()
    .uri()
    .messages({
      'string.uri': 'Project image must be a valid URL',
      'any.required': 'Project image URL is required'
    }),

  project_link: Joi.string()
    .required()
    .uri()
    .messages({
      'string.uri': 'Project link must be a valid URL',
      'any.required': 'Project link is required'
    }),

  project_client: Joi.string()
    .required()
    .min(2)
    .max(100)
    .trim()
    .pattern(/^[a-zA-Z0-9\s\-_.,&()]+$/)
    .messages({
      'string.pattern.base': 'Client name contains invalid characters',
      'string.min': 'Client name must be at least 2 characters long',
      'string.max': 'Client name must not exceed 100 characters',
      'any.required': 'Client name is required'
    }),

  services_provided: Joi.string()
    .valid('Web Development', 'Mobile App Development', 'UI/UX Design', 'Digital Marketing', 'Other')
    .required()
    .messages({
      'any.only': 'Service must be one of: Web Development, Mobile App Development, UI/UX Design, Digital Marketing, Other',
      'any.required': 'Service provided is required'
    }),

  timeline: Joi.string()
    .required()
    .min(3)
    .max(50)
    .trim()
    .messages({
      'string.min': 'Timeline must be at least 3 characters long',
      'string.max': 'Timeline must not exceed 50 characters',
      'any.required': 'Timeline is required'
    }),

  problem_statement: Joi.string()
    .required()
    .min(20)
    .max(1000)
    .trim()
    .messages({
      'string.min': 'Problem statement must be at least 20 characters long',
      'string.max': 'Problem statement must not exceed 1000 characters',
      'any.required': 'Problem statement is required'
    }),

  problem_images: Joi.array()
    .items(Joi.string().uri().messages({
      'string.uri': 'Each problem image must be a valid URL'
    }))
    .default([])
    .max(10)
    .messages({
      'array.max': 'Maximum 10 problem images allowed'
    }),

  solution_images: Joi.array()
    .items(Joi.string().uri().messages({
      'string.uri': 'Each solution image must be a valid URL'
    }))
    .default([])
    .max(10)
    .messages({
      'array.max': 'Maximum 10 solution images allowed'
    })
});

// Partial schema for PATCH operations (all fields optional)
const partialSolutionSchema = solutionSchema.fork(
  Object.keys(solutionSchema.describe().keys),
  (schema) => schema.optional()
);

// Query parameters validation for GET requests
const queryParamsSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10),
  services_provided: Joi.string().valid('Web Development', 'Mobile App Development', 'UI/UX Design', 'Digital Marketing', 'Other'),
  client: Joi.string().min(1).max(100),
  sort: Joi.string().valid('createdAt', '-createdAt', 'project_name', '-project_name', 'updatedAt', '-updatedAt').default('-createdAt')
});

// ID parameter validation
const idParamSchema = Joi.object({
  id: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).required().messages({
    'string.pattern.base': 'Invalid solution ID format'
  })
});

module.exports = {
  solutionSchema,
  partialSolutionSchema,
  queryParamsSchema,
  idParamSchema
};
