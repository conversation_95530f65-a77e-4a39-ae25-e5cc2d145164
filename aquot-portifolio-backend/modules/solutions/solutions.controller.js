const Solution = require("./solutions.model");
const Joi = require("joi");

// Validation schema for solution data
const solutionValidationSchema = Joi.object({
  project_name: Joi.string().required().min(3).max(100),
  project_smallDescription: Joi.string().required().min(10).max(200),
  project_description: Joi.string().required().min(50).max(2000),
  project_image: Joi.string().required().uri(),
  project_link: Joi.string().required().uri(),
  project_client: Joi.string().required().min(2).max(100),
  services_provided: Joi.string()
    .valid(
      "Web Development",
      "Mobile App Development",
      "UI/UX Design",
      "Digital Marketing",
      "Other"
    )
    .required(),
  timeline: Joi.string().required().min(3).max(50),
  problem_statement: Joi.string().required().min(20).max(1000),
  problem_images: Joi.array().items(Joi.string().uri()).default([]),
  solution_images: Joi.array().items(Joi.string().uri()).default([]),
});

// GET /solutions - Get all solutions
const getAllSolutions = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build filter object
    const filter = {};
    if (req.query.services_provided) {
      filter.services_provided = req.query.services_provided;
    }
    if (req.query.client) {
      filter.project_client = new RegExp(req.query.client, "i");
    }

    const solutions = await Solution.find(filter)
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 });

    const total = await Solution.countDocuments(filter);
    const totalPages = Math.ceil(total / limit);

    res.status(200).json({
      success: true,
      data: solutions,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems: total,
        itemsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
    });
  } catch (error) {
    console.error("Error fetching solutions:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching solutions",
      error: error.message,
    });
  }
};

// GET /solutions/:id - Get solution by ID
const getSolutionById = async (req, res) => {
  try {
    const { id } = req.params;

    const solution = await Solution.findById(id);

    if (!solution) {
      return res.status(404).json({
        success: false,
        message: "Solution not found",
      });
    }

    res.status(200).json({
      success: true,
      data: solution,
    });
  } catch (error) {
    console.error("Error fetching solution:", error);

    if (error.name === "CastError") {
      return res.status(400).json({
        success: false,
        message: "Invalid solution ID format",
      });
    }

    res.status(500).json({
      success: false,
      message: "Error fetching solution",
      error: error.message,
    });
  }
};

// POST /solutions - Create new solution
const createSolution = async (req, res) => {
  try {
    // Validate request body
    const { error, value } = solutionValidationSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        success: false,
        message: "Validation error",
        errors: error.details.map((detail) => ({
          field: detail.path.join("."),
          message: detail.message,
        })),
      });
    }

    // Check if solution with same project name already exists
    const existingSolution = await Solution.findOne({
      project_name: value.project_name,
    });

    if (existingSolution) {
      return res.status(409).json({
        success: false,
        message: "Solution with this project name already exists",
      });
    }

    const solution = new Solution(value);
    const savedSolution = await solution.save();

    res.status(201).json({
      success: true,
      message: "Solution created successfully",
      data: savedSolution,
    });
  } catch (error) {
    console.error("Error creating solution:", error);

    if (error.name === "ValidationError") {
      return res.status(400).json({
        success: false,
        message: "Validation error",
        errors: Object.values(error.errors).map((err) => ({
          field: err.path,
          message: err.message,
        })),
      });
    }

    res.status(500).json({
      success: false,
      message: "Error creating solution",
      error: error.message,
    });
  }
};

// PUT /solutions/:id - Update solution
const updateSolution = async (req, res) => {
  try {
    const { id } = req.params;

    // Validate request body
    const { error, value } = solutionValidationSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        success: false,
        message: "Validation error",
        errors: error.details.map((detail) => ({
          field: detail.path.join("."),
          message: detail.message,
        })),
      });
    }

    // Check if solution exists
    const existingSolution = await Solution.findById(id);
    if (!existingSolution) {
      return res.status(404).json({
        success: false,
        message: "Solution not found",
      });
    }

    // Check if another solution with same project name exists (excluding current one)
    const duplicateSolution = await Solution.findOne({
      project_name: value.project_name,
      _id: { $ne: id },
    });

    if (duplicateSolution) {
      return res.status(409).json({
        success: false,
        message: "Another solution with this project name already exists",
      });
    }

    const updatedSolution = await Solution.findByIdAndUpdate(id, value, {
      new: true,
      runValidators: true,
    });

    res.status(200).json({
      success: true,
      message: "Solution updated successfully",
      data: updatedSolution,
    });
  } catch (error) {
    console.error("Error updating solution:", error);

    if (error.name === "CastError") {
      return res.status(400).json({
        success: false,
        message: "Invalid solution ID format",
      });
    }

    if (error.name === "ValidationError") {
      return res.status(400).json({
        success: false,
        message: "Validation error",
        errors: Object.values(error.errors).map((err) => ({
          field: err.path,
          message: err.message,
        })),
      });
    }

    res.status(500).json({
      success: false,
      message: "Error updating solution",
      error: error.message,
    });
  }
};

// DELETE /solutions/:id - Delete solution
const deleteSolution = async (req, res) => {
  try {
    const { id } = req.params;

    const solution = await Solution.findById(id);

    if (!solution) {
      return res.status(404).json({
        success: false,
        message: "Solution not found",
      });
    }

    await Solution.findByIdAndDelete(id);

    res.status(200).json({
      success: true,
      message: "Solution deleted successfully",
      data: solution,
    });
  } catch (error) {
    console.error("Error deleting solution:", error);

    if (error.name === "CastError") {
      return res.status(400).json({
        success: false,
        message: "Invalid solution ID format",
      });
    }

    res.status(500).json({
      success: false,
      message: "Error deleting solution",
      error: error.message,
    });
  }
};

// PATCH /solutions/:id - Partial update solution
const patchSolution = async (req, res) => {
  try {
    const { id } = req.params;

    // Create a partial validation schema (all fields optional)
    const partialSchema = solutionValidationSchema.fork(
      Object.keys(solutionValidationSchema.describe().keys),
      (schema) => schema.optional()
    );

    // Validate request body
    const { error, value } = partialSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        success: false,
        message: "Validation error",
        errors: error.details.map((detail) => ({
          field: detail.path.join("."),
          message: detail.message,
        })),
      });
    }

    // Check if solution exists
    const existingSolution = await Solution.findById(id);
    if (!existingSolution) {
      return res.status(404).json({
        success: false,
        message: "Solution not found",
      });
    }

    // Check if project name is being updated and if it conflicts
    if (value.project_name) {
      const duplicateSolution = await Solution.findOne({
        project_name: value.project_name,
        _id: { $ne: id },
      });

      if (duplicateSolution) {
        return res.status(409).json({
          success: false,
          message: "Another solution with this project name already exists",
        });
      }
    }

    const updatedSolution = await Solution.findByIdAndUpdate(id, value, {
      new: true,
      runValidators: true,
    });

    res.status(200).json({
      success: true,
      message: "Solution updated successfully",
      data: updatedSolution,
    });
  } catch (error) {
    console.error("Error updating solution:", error);

    if (error.name === "CastError") {
      return res.status(400).json({
        success: false,
        message: "Invalid solution ID format",
      });
    }

    if (error.name === "ValidationError") {
      return res.status(400).json({
        success: false,
        message: "Validation error",
        errors: Object.values(error.errors).map((err) => ({
          field: err.path,
          message: err.message,
        })),
      });
    }

    res.status(500).json({
      success: false,
      message: "Error updating solution",
      error: error.message,
    });
  }
};

module.exports = {
  getAllSolutions,
  getSolutionById,
  createSolution,
  updateSolution,
  deleteSolution,
  patchSolution,
};
