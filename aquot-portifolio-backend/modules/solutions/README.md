# Solutions API Documentation

This module provides a complete CRUD (Create, Read, Update, Delete) API for managing portfolio solutions.

## Base URL
```
/api/solutions
```

## Endpoints

### 1. Get All Solutions
**GET** `/api/solutions`

Retrieves a paginated list of all solutions with optional filtering.

**Query Parameters:**
- `page` (number, optional): Page number (default: 1)
- `limit` (number, optional): Items per page (default: 10, max: 100)
- `services_provided` (string, optional): Filter by service type
- `client` (string, optional): Filter by client name (case-insensitive)
- `sort` (string, optional): Sort field (default: -createdAt)

**Example Request:**
```bash
GET /api/solutions?page=1&limit=5&services_provided=Web Development
```

**Response:**
```json
{
  "success": true,
  "data": [...],
  "pagination": {
    "currentPage": 1,
    "totalPages": 3,
    "totalItems": 25,
    "itemsPerPage": 10,
    "hasNextPage": true,
    "hasPrevPage": false
  }
}
```

### 2. Get Solution by ID
**GET** `/api/solutions/:id`

Retrieves a specific solution by its ID.

**Parameters:**
- `id` (string): MongoDB ObjectId of the solution

**Example Request:**
```bash
GET /api/solutions/68a3613a0881aa7431de705f
```

**Response:**
```json
{
  "success": true,
  "data": {
    "_id": "68a3613a0881aa7431de705f",
    "project_name": "E-commerce Platform",
    "project_smallDescription": "A modern e-commerce platform",
    "project_description": "Full description...",
    "project_image": "https://example.com/image.jpg",
    "project_link": "https://example.com/project",
    "project_client": "Client Name",
    "services_provided": "Web Development",
    "timeline": "3 months",
    "problem_statement": "Problem description...",
    "problem_images": ["https://example.com/problem1.jpg"],
    "solution_images": ["https://example.com/solution1.jpg"],
    "createdAt": "2025-01-18T...",
    "updatedAt": "2025-01-18T..."
  }
}
```

### 3. Create New Solution
**POST** `/api/solutions`

Creates a new solution.

**Request Body:**
```json
{
  "project_name": "Project Name",
  "project_smallDescription": "Short description (10-200 chars)",
  "project_description": "Detailed description (50-2000 chars)",
  "project_image": "https://example.com/image.jpg",
  "project_link": "https://example.com/project",
  "project_client": "Client Name",
  "services_provided": "Web Development",
  "timeline": "3 months",
  "problem_statement": "Problem description (20-1000 chars)",
  "problem_images": ["https://example.com/problem1.jpg"],
  "solution_images": ["https://example.com/solution1.jpg"]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Solution created successfully",
  "data": { ... }
}
```

### 4. Update Solution (Full Update)
**PUT** `/api/solutions/:id`

Updates all fields of an existing solution.

**Parameters:**
- `id` (string): MongoDB ObjectId of the solution

**Request Body:** Same as POST request (all fields required)

**Response:**
```json
{
  "success": true,
  "message": "Solution updated successfully",
  "data": { ... }
}
```

### 5. Partial Update Solution
**PATCH** `/api/solutions/:id`

Updates specific fields of an existing solution.

**Parameters:**
- `id` (string): MongoDB ObjectId of the solution

**Request Body:** Any subset of the solution fields

**Example Request:**
```json
{
  "timeline": "4 months",
  "project_description": "Updated description"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Solution updated successfully",
  "data": { ... }
}
```

### 6. Delete Solution
**DELETE** `/api/solutions/:id`

Deletes a solution by its ID.

**Parameters:**
- `id` (string): MongoDB ObjectId of the solution

**Response:**
```json
{
  "success": true,
  "message": "Solution deleted successfully",
  "data": { ... }
}
```

## Data Validation

### Required Fields
- `project_name`: 3-100 characters
- `project_smallDescription`: 10-200 characters
- `project_description`: 50-2000 characters
- `project_image`: Valid URL
- `project_link`: Valid URL
- `project_client`: 2-100 characters
- `services_provided`: One of: "Web Development", "Mobile App Development", "UI/UX Design", "Digital Marketing", "Other"
- `timeline`: 3-50 characters
- `problem_statement`: 20-1000 characters

### Optional Fields
- `problem_images`: Array of valid URLs (max 10)
- `solution_images`: Array of valid URLs (max 10)

## Error Responses

### Validation Error (400)
```json
{
  "success": false,
  "message": "Validation error",
  "errors": [
    {
      "field": "project_name",
      "message": "Project name is required"
    }
  ]
}
```

### Not Found (404)
```json
{
  "success": false,
  "message": "Solution not found"
}
```

### Conflict (409)
```json
{
  "success": false,
  "message": "Solution with this project name already exists"
}
```

### Rate Limit (429)
```json
{
  "success": false,
  "message": "Too many requests from this IP, please try again later.",
  "retryAfter": "15 minutes"
}
```

## Rate Limiting

- General API calls: 100 requests per 15 minutes per IP
- Solution creation: 10 requests per 15 minutes per IP

## Security Features

- Input sanitization (XSS protection)
- Rate limiting
- Request logging
- Helmet security headers
- CORS enabled
- Data validation with Joi

## Testing

Run the test suite:
```bash
node test-solutions.js
```

Make sure the server is running on port 3000 before running tests.
