const mongoose = require("mongoose");

const solutionSchema = new mongoose.Schema(
  {
    project_name: {
      type: String,
      required: true,
    },
    project_smallDescription: {
      type: String,
      required: true,
    },
    project_description: {
      type: String,
      required: true,
    },
    project_image: {
      type: String,
      required: true,
    },
    project_link: {
      type: String,
      required: true,
    },
    project_client: {
      type: String,
      required: true,
    },
    services_provided: {
      enum: [
        "Web Development",
        "Mobile App Development",
        "UI/UX Design",
        "Digital Marketing",
        "Other",
      ],
      type: String,
      required: true,
    },
    timeline: {
      type: String,
      required: true,
    },
    problem_statement: {
      type: String,
      required: true,
    },
    problem_images: {
      type: [String],
      default: [],
    },
    solution_images: {
      type: [String],
      default: [],
    },
  },
  {
    timestamps: true, // This adds createdAt and updatedAt fields
  }
);

const Solution = mongoose.model("Solution", solutionSchema);

module.exports = Solution;
