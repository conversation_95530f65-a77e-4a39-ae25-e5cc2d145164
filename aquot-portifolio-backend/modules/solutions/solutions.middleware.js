const Joi = require("joi");
const rateLimit = require("express-rate-limit");

// Validation schema for solution data
const solutionValidationSchema = Joi.object({
  project_name: Joi.string().required().min(3).max(100).trim(),
  project_smallDescription: Joi.string().required().min(10).max(200).trim(),
  project_description: Joi.string().required().min(50).max(2000).trim(),
  project_image: Joi.string().required().uri(),
  project_link: Joi.string().required().uri(),
  project_client: Joi.string().required().min(2).max(100).trim(),
  services_provided: Joi.string()
    .valid(
      "Web Development",
      "Mobile App Development",
      "UI/UX Design",
      "Digital Marketing",
      "Other"
    )
    .required(),
  timeline: Joi.string().required().min(3).max(50).trim(),
  problem_statement: Joi.string().required().min(20).max(1000).trim(),
  problem_images: Joi.array().items(Joi.string().uri()).default([]),
  solution_images: Joi.array().items(Joi.string().uri()).default([]),
});

// Rate limiting middleware for solutions API
const solutionsRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    success: false,
    message: "Too many requests from this IP, please try again later.",
    retryAfter: "15 minutes",
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Create solutions rate limit (more restrictive)
const createSolutionsRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // limit each IP to 10 create requests per windowMs
  message: {
    success: false,
    message:
      "Too many solution creation requests from this IP, please try again later.",
    retryAfter: "15 minutes",
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Validation middleware
const validateSolution = (req, res, next) => {
  const { error, value } = solutionValidationSchema.validate(req.body, {
    abortEarly: false,
    stripUnknown: true,
  });

  if (error) {
    return res.status(400).json({
      success: false,
      message: "Validation error",
      errors: error.details.map((detail) => ({
        field: detail.path.join("."),
        message: detail.message,
        value: detail.context.value,
      })),
    });
  }

  req.validatedData = value;
  next();
};

// Error handling middleware
const handleValidationErrors = (req, res, next) => {
  // This middleware can be used for additional validation logic
  // For now, it just passes through
  next();
};

// Sanitize input middleware
const sanitizeInput = (req, res, next) => {
  if (req.body) {
    // Remove any potential XSS attempts
    Object.keys(req.body).forEach((key) => {
      if (typeof req.body[key] === "string") {
        req.body[key] = req.body[key]
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, "")
          .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, "")
          .replace(/javascript:/gi, "")
          .replace(/on\w+\s*=/gi, "");
      }
    });
  }
  next();
};

// Logging middleware
const logRequest = (req, res, next) => {
  const timestamp = new Date().toISOString();
  const method = req.method;
  const url = req.originalUrl;
  const ip = req.ip || req.connection.remoteAddress;

  console.log(`[${timestamp}] ${method} ${url} - IP: ${ip}`);
  next();
};

// Check if solution exists middleware
const checkSolutionExists = async (req, res, next) => {
  try {
    const Solution = require("./solutions.model");
    const { id } = req.params;

    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      return res.status(400).json({
        success: false,
        message: "Invalid solution ID format",
      });
    }

    const solution = await Solution.findById(id);

    if (!solution) {
      return res.status(404).json({
        success: false,
        message: "Solution not found",
      });
    }

    req.solution = solution;
    next();
  } catch (error) {
    console.error("Error checking solution existence:", error);
    res.status(500).json({
      success: false,
      message: "Error checking solution existence",
      error: error.message,
    });
  }
};

// Pagination middleware
const validatePagination = (req, res, next) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;

  // Validate pagination parameters
  if (page < 1) {
    return res.status(400).json({
      success: false,
      message: "Page number must be greater than 0",
    });
  }

  if (limit < 1 || limit > 100) {
    return res.status(400).json({
      success: false,
      message: "Limit must be between 1 and 100",
    });
  }

  req.pagination = { page, limit };
  next();
};

module.exports = {
  validateSolution,
  handleValidationErrors,
  sanitizeInput,
  logRequest,
  checkSolutionExists,
  validatePagination,
  solutionsRateLimit,
  createSolutionsRateLimit,
  solutionValidationSchema,
};
