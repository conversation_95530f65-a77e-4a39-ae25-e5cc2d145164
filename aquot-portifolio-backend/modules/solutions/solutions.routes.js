const express = require("express");
const router = express.Router();
const {
  getAllSolutions,
  getSolutionById,
  createSolution,
  updateSolution,
  deleteSolution,
  patchSolution,
} = require("./solutions.controller");
const {
  validateSolution,
  handleValidationErrors,
  sanitizeInput,
  logRequest,
  validatePagination,
  solutionsRateLimit,
  createSolutionsRateLimit,
} = require("./solutions.middleware");

// Apply rate limiting to all routes
router.use(solutionsRateLimit);

// Apply logging to all routes
router.use(logRequest);

// Apply input sanitization to all routes
router.use(sanitizeInput);

// GET /solutions - Get all solutions with pagination and filtering
router.get("/", validatePagination, getAllSolutions);

// GET /solutions/:id - Get solution by ID
router.get("/:id", getSolutionById);

// POST /solutions - Create new solution
router.post(
  "/",
  createSolutionsRateLimit,
  validateSolution,
  handleValidationErrors,
  createSolution
);

// PUT /solutions/:id - Update solution (full update)
router.put("/:id", validateSolution, handleValidationErrors, updateSolution);

// PATCH /solutions/:id - Partial update solution
router.patch("/:id", handleValidationErrors, patchSolution);

// DELETE /solutions/:id - Delete solution
router.delete("/:id", deleteSolution);

module.exports = router;
