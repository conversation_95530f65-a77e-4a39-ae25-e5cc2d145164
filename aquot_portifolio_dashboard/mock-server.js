const express = require('express');
const cors = require('cors');
const app = express();

app.use(cors());
app.use(express.json());

// Mock solutions data
let solutions = [
  {
    _id: '1',
    project_name: 'E-commerce Platform',
    project_smallDescription: 'A modern e-commerce platform for small businesses',
    project_description: 'This comprehensive e-commerce platform provides small businesses with all the tools they need to sell online. Features include inventory management, payment processing, order tracking, and customer management.',
    project_image: 'https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=800',
    project_link: 'https://example.com/ecommerce',
    project_client: 'TechCorp Solutions',
    services_provided: 'Web Development',
    timeline: '4 months',
    problem_statement: 'The client needed a scalable e-commerce solution to replace their outdated system and improve customer experience.',
    problem_images: ['https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=400'],
    solution_images: ['https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=600'],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    _id: '2',
    project_name: 'Mobile Banking App',
    project_smallDescription: 'Secure mobile banking application with biometric authentication',
    project_description: 'A comprehensive mobile banking app that allows users to manage their accounts, transfer money, pay bills, and access financial services securely from their mobile devices.',
    project_image: 'https://images.unsplash.com/photo-**********-824ae1b704d3?w=800',
    project_link: 'https://example.com/banking-app',
    project_client: 'First National Bank',
    services_provided: 'Mobile App Development',
    timeline: '6 months',
    problem_statement: 'The bank needed a modern mobile app to compete with fintech companies and provide better customer service.',
    problem_images: ['https://images.unsplash.com/photo-**********-824ae1b704d3?w=400'],
    solution_images: ['https://images.unsplash.com/photo-**********-824ae1b704d3?w=600'],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

// GET /api/solutions - Get all solutions
app.get('/api/solutions', (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const skip = (page - 1) * limit;
  
  let filteredSolutions = solutions;
  
  // Filter by service type
  if (req.query.services_provided) {
    filteredSolutions = filteredSolutions.filter(s => 
      s.services_provided === req.query.services_provided
    );
  }
  
  // Filter by client name
  if (req.query.client) {
    filteredSolutions = filteredSolutions.filter(s => 
      s.project_client.toLowerCase().includes(req.query.client.toLowerCase())
    );
  }
  
  const paginatedSolutions = filteredSolutions.slice(skip, skip + limit);
  const totalPages = Math.ceil(filteredSolutions.length / limit);
  
  res.json({
    success: true,
    data: paginatedSolutions,
    pagination: {
      currentPage: page,
      totalPages,
      totalItems: filteredSolutions.length,
      itemsPerPage: limit,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1
    }
  });
});

// GET /api/solutions/:id - Get solution by ID
app.get('/api/solutions/:id', (req, res) => {
  const solution = solutions.find(s => s._id === req.params.id);
  
  if (!solution) {
    return res.status(404).json({
      success: false,
      message: 'Solution not found'
    });
  }
  
  res.json({
    success: true,
    data: solution
  });
});

// POST /api/solutions - Create new solution
app.post('/api/solutions', (req, res) => {
  const newSolution = {
    _id: (solutions.length + 1).toString(),
    ...req.body,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  solutions.push(newSolution);
  
  res.status(201).json({
    success: true,
    message: 'Solution created successfully',
    data: newSolution
  });
});

// PUT /api/solutions/:id - Update solution
app.put('/api/solutions/:id', (req, res) => {
  const index = solutions.findIndex(s => s._id === req.params.id);
  
  if (index === -1) {
    return res.status(404).json({
      success: false,
      message: 'Solution not found'
    });
  }
  
  solutions[index] = {
    ...solutions[index],
    ...req.body,
    updatedAt: new Date().toISOString()
  };
  
  res.json({
    success: true,
    message: 'Solution updated successfully',
    data: solutions[index]
  });
});

// DELETE /api/solutions/:id - Delete solution
app.delete('/api/solutions/:id', (req, res) => {
  const index = solutions.findIndex(s => s._id === req.params.id);
  
  if (index === -1) {
    return res.status(404).json({
      success: false,
      message: 'Solution not found'
    });
  }
  
  solutions.splice(index, 1);
  
  res.json({
    success: true,
    message: 'Solution deleted successfully'
  });
});

const PORT = 3001;
app.listen(PORT, () => {
  console.log(`Mock server running on http://localhost:${PORT}`);
});
