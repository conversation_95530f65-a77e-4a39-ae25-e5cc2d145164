'use client';

import { useEffect, useState } from 'react';
import Layout from '@/components/layouts/Layout';
import Modal from '@/components/ui/Modal';
import { apiService } from '@/lib/api';
import {
  Star,
  AlertTriangle,
  Check,
  X,
  MessageSquare,
  Calendar,
  User,
  Flag,
} from 'lucide-react';

export default function ReviewsPage() {
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedReview, setSelectedReview] = useState(null);
  const [actionModal, setActionModal] = useState({ isOpen: false, review: null, action: null });

  useEffect(() => {
    fetchReportedReviews();
  }, []);

  const fetchReportedReviews = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiService.getReportedReviews();
      console.log('API Response:', response);
      const reviewsData = response.data?.data.reviews || [];
      if (!Array.isArray(reviewsData)) {
        console.warn('Unexpected reviews data structure:', response);
        setError('Invalid response format from server.');
        setReviews([]);
      } else {
        setReviews(reviewsData);
      }
    } catch (error) {
      console.error('Failed to fetch reviews:', error.message, error.stack);
      setError('Failed to load reviews. Please try again later.');
      setReviews([]);
    } finally {
      setLoading(false);
    }
  };

  const handleReviewAction = async (reviewId, action) => {
    try {
      await apiService.manageReview(reviewId, action);
      setReviews(reviews.filter(r => r._id !== reviewId));
      setActionModal({ isOpen: false, review: null, action: null });
    } catch (error) {
      console.error(`Failed to ${action} review:`, error.message, error.stack);
    }
  };

  const getStarRating = (rating) => {
    const validRating = Number.isInteger(rating) && rating >= 0 && rating <= 5 ? rating : 0;
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${i < validRating ? 'text-[#FF9100] fill-current' : 'text-gray-600'}`}
      />
    ));
  };

  return (
    <Layout title="Reviews">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">Review Management</h1>
            <p className="text-gray-400 mt-1">Moderate reported reviews and feedback</p>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-400">
            <AlertTriangle className="w-4 h-4 text-[#FF9100]" />
            <span>{reviews.length} reported reviews</span>
          </div>
        </div>

        {/* Content */}
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#FF9100]"></div>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <div className="mx-auto w-24 h-24 bg-background/40 backdrop-blur-xl rounded-full flex items-center justify-center mb-4 border border-white/10">
              <AlertTriangle className="w-8 h-8 text-red-400" />
            </div>
            <h3 className="text-lg font-medium text-white mb-2">Error Loading Reviews</h3>
            <p className="text-gray-400">{error}</p>
            <button
              onClick={fetchReportedReviews}
              className="mt-4 px-4 py-2 bg-[#FF9100] text-white rounded-lg hover:bg-[#e07b00]"
            >
              Retry
            </button>
          </div>
        ) : reviews.length === 0 ? (
          <div className="text-center py-12">
            <div className="mx-auto w-24 h-24 bg-background/40 backdrop-blur-xl rounded-full flex items-center justify-center mb-4 border border-white/10">
              <MessageSquare className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-white mb-2">No reported reviews</h3>
            <p className="text-gray-400">All reviews are currently approved and published.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {reviews.map((review) => (
              <ReviewCard
                key={review._id}
                review={review}
                onViewDetails={setSelectedReview}
                onAction={(review, action) => setActionModal({ isOpen: true, review, action })}
              />
            ))}
          </div>
        )}
      </div>

      {/* Review Details Modal */}
      <Modal
        isOpen={!!selectedReview}
        onClose={() => setSelectedReview(null)}
        title="Review Details"
        size="lg"
      >
        {selectedReview && (
          <div className="space-y-6">
            <div className="flex items-start justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-[#FF9100] rounded-full flex items-center justify-center">
                  <User className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h4 className="font-medium text-white">
                    {selectedReview.userNickname || 'Anonymous User'}
                  </h4>
                  <div className="flex items-center space-x-1">
                    {getStarRating(selectedReview.rating)}
                  </div>
                </div>
              </div>
              <div className="text-right text-sm text-gray-400">
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-1" />
                  {new Date(selectedReview.createdAt).toLocaleDateString()}
                </div>
              </div>
            </div>

            {/* Product */}
            <div>
              <h5 className="font-medium text-white mb-2">Product</h5>
              <p className="text-gray-300">{selectedReview.product?.name || 'Unknown Product'}</p>
            </div>

            {/* Review Text */}
            <div>
              <h5 className="font-medium text-white mb-2">Review Text</h5>
              <p className="text-gray-300 bg-background/40 backdrop-blur-xl rounded-lg p-4 border border-white/10">
                {selectedReview.reviewText || 'No review text provided'}
              </p>
            </div>

            {/* Images */}
            {selectedReview.images && selectedReview.images.length > 0 && (
              <div>
                <h5 className="font-medium text-white mb-2">Images</h5>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {selectedReview.images.map((image, index) => (
                    // eslint-disable-next-line @next/next/no-img-element
                    <img
                      key={index}
                      src={image.url || image} // Adjust based on image structure
                      alt={`Review image ${index + 1}`}
                      className="w-full h-24 object-cover rounded-lg border border-white/10"
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Report Reasons */}
            <div>
              <h5 className="font-medium text-white mb-2">Report Reasons</h5>
              <div className="flex flex-wrap gap-2">
                {(selectedReview.reportReasons || ['Reported']).map((reason, index) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-red-500/20 text-red-400 rounded-full text-sm"
                  >
                    {reason}
                  </span>
                ))}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 pt-4 border-t border-white/10">
              <button
                onClick={() =>
                  setActionModal({ isOpen: true, review: selectedReview, action: 'approve' })
                }
                className="flex items-center px-4 py-2 bg-green-600/80 hover:bg-green-700 text-white rounded-lg transition-colors"
              >
                <Check className="w-4 h-4 mr-2" />
                Approve
              </button>
              <button
                onClick={() =>
                  setActionModal({ isOpen: true, review: selectedReview, action: 'delete' })
                }
                className="flex items-center px-4 py-2 bg-red-600/80 hover:bg-red-700 text-white rounded-lg transition-colors"
              >
                <X className="w-4 h-4 mr-2" />
                Delete
              </button>
            </div>
          </div>
        )}
      </Modal>

      {/* Action Confirmation Modal */}
      <Modal
        isOpen={actionModal.isOpen}
        onClose={() => setActionModal({ isOpen: false, review: null, action: null })}
        title={`${actionModal.action === 'approve' ? 'Approve' : 'Delete'} Review`}
      >
        <div className="space-y-4">
          <p className="text-gray-300">
            Are you sure you want to {actionModal.action} this review? This action cannot be undone.
          </p>
          <div className="flex justify-end space-x-3">
            <button
              onClick={() => setActionModal({ isOpen: false, review: null, action: null })}
              className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={() => handleReviewAction(actionModal.review?._id, actionModal.action)}
              className={`px-4 py-2 rounded-lg transition-colors ${
                actionModal.action === 'approve'
                  ? 'bg-green-600 hover:bg-green-700 text-white'
                  : 'bg-red-600 hover:bg-red-700 text-white'
              }`}
            >
              {actionModal.action === 'approve' ? 'Approve' : 'Delete'}
            </button>
          </div>
        </div>
      </Modal>
    </Layout>
  );
}

function ReviewCard({ review, onViewDetails, onAction }) {
  if (!review) {
    return null;
  }

  const getStarRating = (rating) => {
    const validRating = Number.isInteger(rating) && rating >= 0 && rating <= 5 ? rating : 0;
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${i < validRating ? 'text-[#FF9100] fill-current' : 'text-gray-600'}`}
      />
    ));
  };

  return (
    <div className="bg-background/40 backdrop-blur-xl rounded-lg border border-white/10 p-6 transition hover:shadow-lg hover:shadow-black/40">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-[#FF9100] rounded-full flex items-center justify-center">
            <User className="w-5 h-5 text-white" />
          </div>
          <div>
            <h4 className="font-medium text-white">
              {review.userNickname || 'Anonymous User'}
            </h4>
            <div className="flex items-center space-x-1">
              {getStarRating(review.rating)}
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Flag className="w-4 h-4 text-red-400" />
          <span className="text-xs text-red-400">Reported</span>
        </div>
      </div>

      <div className="mb-4">
        <p className="text-sm text-gray-400 mb-1">
          Product: {review.product?.name || 'Unknown Product'}
        </p>
        <p className="text-gray-300 line-clamp-2">
          {review.reviewText || 'No review text provided'}
        </p>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 text-sm text-gray-400">
          <div className="flex items-center">
            <Calendar className="w-4 h-4 mr-1" />
            {review.createdAt
              ? new Date(review.createdAt).toLocaleDateString()
              : 'Unknown Date'}
          </div>
          <div className="flex items-center">
            <MessageSquare className="w-4 h-4 mr-1" />
            {review.reportCount || 1} reports
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => onViewDetails(review)}
            className="px-3 py-1 text-sm text-blue-400 hover:bg-blue-500/20 rounded transition-colors"
          >
            View Details
          </button>
          <button
            onClick={() => onAction(review, 'approve')}
            className="flex items-center px-3 py-1 text-sm text-green-400 hover:bg-green-500/20 rounded transition-colors"
          >
            <Check className="w-4 h-4 mr-1" />
            Approve
          </button>
          <button
            onClick={() => onAction(review, 'delete')}
            className="flex items-center px-3 py-1 text-sm text-red-400 hover:bg-red-500/20 rounded transition-colors"
          >
            <X className="w-4 h-4 mr-1" />
            Delete
          </button>
        </div>
      </div>
    </div>
  );
}