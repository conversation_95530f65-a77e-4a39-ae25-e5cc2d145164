'use client';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Layout from '@/components/layouts/Layout';
import { Card, CardHeader, CardContent, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { apiService } from '@/lib/api';
import { ArrowLeft, Upload, X } from 'lucide-react';
import toast from 'react-hot-toast';

const CreateProduct = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [uploadingFile, setUploadingFile] = useState(false);
  const [uploadingScreenshots, setUploadingScreenshots] = useState(false);

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: 'Android',
    price: 0,
    version: '',
    fileSize: 0,
    fileUrl: '',
    screenshots: [],
    videos: [],
    tags: [],
    featured: false,
    systemRequirements: '',
    developer: 'Aquot Team'
  });

  const [tagInput, setTagInput] = useState('');
  const [errors, setErrors] = useState({});

  const categories = ['Android', 'iOS', 'Web', 'Desktop'];

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    if (errors[name]) setErrors(prev => ({ ...prev, [name]: '' }));
  };

  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    try {
      setUploadingFile(true);
      const response = await apiService.uploadFile(file);
      setFormData(prev => ({
        ...prev,
        fileUrl: response.data.data.fileUrl,
        fileSize: file.size
      }));
      toast.success('File uploaded successfully');
    } catch (error) {
      toast.error('Failed to upload file');
    } finally {
      setUploadingFile(false);
    }
  };

  const handleScreenshotUpload = async (e) => {
    const files = Array.from(e.target.files);
    if (files.length === 0) return;

    try {
      setUploadingScreenshots(true);
      const response = await apiService.uploadMultipleFiles(files);
      const newScreenshots = response.data.data.fileUrls.map(url => ({ url, alt: '' }));
      setFormData(prev => ({
        ...prev,
        screenshots: [...prev.screenshots, ...newScreenshots]
      }));
      toast.success('Screenshots uploaded successfully');
    } catch (error) {
      toast.error('Failed to upload screenshots');
    } finally {
      setUploadingScreenshots(false);
    }
  };

  const removeScreenshot = (index) => {
    setFormData(prev => ({
      ...prev,
      screenshots: prev.screenshots.filter((_, i) => i !== index)
    }));
  };

  const addTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.name.trim()) newErrors.name = 'Product name is required';
    if (!formData.description.trim()) newErrors.description = 'Description is required';
    if (!formData.version.trim()) newErrors.version = 'Version is required';
    if (!formData.fileUrl) newErrors.fileUrl = 'Product file is required';
    if (formData.price < 0) newErrors.price = 'Price cannot be negative';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) {
      toast.error('Please fix the errors before submitting');
      return;
    }
    try {
      setLoading(true);
      await apiService.createProduct(formData);
      toast.success('Product created successfully');
      router.push('/products');
    } catch (error) {
      console.log(error);
      toast.error('Failed to create product');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout title="Create Product">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div>
            <h2 className="text-lg font-medium text-white">Create New Product</h2>
            <p className="text-sm text-white/70">Add a new product to the marketplace</p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Info */}
          <Card className="bg-[#171717]/80 backdrop-blur-md border-white/20">
            <CardHeader>
              <CardTitle className="text-white">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Product Name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  error={errors.name}
                  className="bg-[#0D0D0D]/70 text-white placeholder-white/50 border-white/20 focus:ring-[#FF9100] focus:border-[#FF9100]"
                  required
                />
                <div>
                  <label className="block text-sm font-medium text-white mb-1">Category</label>
                  <select
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                    className="block w-full px-3 py-2 border border-white/20 bg-[#0D0D0D]/70 text-white rounded-md shadow-sm focus:outline-none focus:ring-[#FF9100] focus:border-[#FF9100] sm:text-sm"
                  >
                    {categories.map(c => <option key={c} value={c}>{c}</option>)}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-white mb-1">Description</label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={4}
                  className="block w-full px-3 py-2 border border-white/20 bg-[#0D0D0D]/70 text-white rounded-md shadow-sm focus:outline-none focus:ring-[#FF9100] focus:border-[#FF9100] sm:text-sm"
                  placeholder="Describe your product..."
                />
                {errors.description && <p className="text-sm text-red-600 mt-1">{errors.description}</p>}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Input
                  label="Price (RWF)"
                  name="price"
                  type="number"
                  min="0"
                  value={formData.price}
                  onChange={handleInputChange}
                  error={errors.price}
                  className="bg-[#0D0D0D]/70 text-white placeholder-white/50 border-white/20 focus:ring-[#FF9100] focus:border-[#FF9100]"
                />
                <Input
                  label="Version"
                  name="version"
                  value={formData.version}
                  onChange={handleInputChange}
                  error={errors.version}
                  placeholder="e.g., 1.0.0"
                  required
                  className="bg-[#0D0D0D]/70 text-white placeholder-white/50 border-white/20 focus:ring-[#FF9100] focus:border-[#FF9100]"
                />
                <Input
                  label="Developer"
                  name="developer"
                  value={formData.developer}
                  onChange={handleInputChange}
                  className="bg-[#0D0D0D]/70 text-white placeholder-white/50 border-white/20 focus:ring-[#FF9100] focus:border-[#FF9100]"
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="featured"
                  checked={formData.featured}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-[#FF9100] focus:ring-[#FF9100] border-white/20 rounded"
                />
                <label className="ml-2 block text-sm text-white">Featured Product</label>
              </div>
            </CardContent>
          </Card>

          {/* Product File */}
          <Card className="bg-[#171717]/80 backdrop-blur-md border-white/20">
            <CardHeader>
              <CardTitle className="text-white">Product File</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-white mb-2">Upload Product File</label>
                <div className="flex items-center space-x-4">
                  <input
                    type="file"
                    onChange={handleFileUpload}
                    className="block w-full text-sm text-white file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-[#0D0D0D]/70 file:text-[#FF9100] hover:file:bg-[#1a1a1a]"
                    accept=".apk,.ipa,.zip,.dmg,.exe,.msi,.deb,.rpm"
                  />
                  {uploadingFile && <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-[#FF9100]"></div>}
                </div>
                {formData.fileUrl && <p className="text-sm text-green-400 mt-2">✓ File uploaded successfully</p>}
                {errors.fileUrl && <p className="text-sm text-red-600 mt-1">{errors.fileUrl}</p>}
              </div>
            </CardContent>
          </Card>

          {/* Screenshots */}
          <Card className="bg-[#171717]/80 backdrop-blur-md border-white/20">
            <CardHeader>
              <CardTitle className="text-white">Screenshots</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-white mb-2">Upload Screenshots</label>
                <div className="flex items-center space-x-4">
                  <input
                    type="file"
                    multiple
                    onChange={handleScreenshotUpload}
                    className="block w-full text-sm text-white file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-[#0D0D0D]/70 file:text-[#FF9100] hover:file:bg-[#1a1a1a]"
                    accept="image/*"
                  />
                  {uploadingScreenshots && <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-[#FF9100]"></div>}
                </div>
              </div>

              {formData.screenshots.length > 0 && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {formData.screenshots.map((s, i) => (
                    <div key={i} className="relative">
                      <img src={s.url} alt={`Screenshot ${i + 1}`} className="w-full h-32 object-cover rounded-lg border border-white/20" />
                      <button
                        type="button"
                        onClick={() => removeScreenshot(i)}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Additional Info */}
          <Card className="bg-[#171717]/80 backdrop-blur-md border-white/20">
            <CardHeader>
              <CardTitle className="text-white">Additional Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-white mb-1">System Requirements</label>
                <textarea
                  name="systemRequirements"
                  value={formData.systemRequirements}
                  onChange={handleInputChange}
                  rows={3}
                  className="block w-full px-3 py-2 border border-white/20 bg-[#0D0D0D]/70 text-white rounded-md shadow-sm focus:outline-none focus:ring-[#FF9100] focus:border-[#FF9100] sm:text-sm"
                  placeholder="e.g., Android 5.0+, 2GB RAM, 100MB storage"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-white mb-2">Tags</label>
                <div className="flex space-x-2 mb-2">
                  <Input
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    placeholder="Add a tag..."
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                    className="bg-[#0D0D0D]/70 text-white placeholder-white/50 border-white/20 focus:ring-[#FF9100] focus:border-[#FF9100]"
                  />
                  <Button type="button" onClick={addTag} variant="outline">
                    Add
                  </Button>
                </div>
                {formData.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, i) => (
                      <span key={i} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-[#1a1a1a] text-[#FF9100]">
                        {tag}
                        <button type="button" onClick={() => removeTag(tag)} className="ml-1 text-[#FF9100] hover:text-white">
                          <X className="h-3 w-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Submit */}
          <div className="flex justify-end space-x-4">
            <Button type="button" variant="outline" onClick={() => router.back()}>Cancel</Button>
            <Button type="submit" loading={loading}>Create Product</Button>
          </div>
        </form>
      </div>
    </Layout>
  );
};

export default CreateProduct;
