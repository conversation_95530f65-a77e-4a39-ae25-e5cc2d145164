'use client';

import { useState } from 'react';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import Button from '@/components/ui/Button';
import { <PERSON>, CardHeader, CardContent, CardTitle } from '@/components/ui/Card';
import Input from '@/components/ui/Input';
import MainLayout from '@/components/layouts/Layout';
import apiClient from '@/lib/api';
import {
  Upload,
  File,
  Image,
  CheckCircle,
  XCircle,
  Copy,
  Trash2,
} from 'lucide-react';

export default function UploadPage() {
  const [files, setFiles] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [uploadResults, setUploadResults] = useState([]);

  const handleFileSelect = (e) => {
    const selectedFiles = Array.from(e.target.files);
    const newFiles = selectedFiles.map((file) => ({
      id: Math.random().toString(36).substr(2, 9),
      file,
      name: file.name,
      size: file.size,
      type: file.type,
      status: 'pending',
      url: null,
      error: null,
    }));
    setFiles((prev) => [...prev, ...newFiles]);
  };

  const removeFile = (fileId) => {
    setFiles(files.filter((f) => f.id !== fileId));
  };

  const uploadFiles = async () => {
    if (files.length === 0) return;

    setUploading(true);
    const results = [];

    try {
      for (const fileItem of files) {
        if (fileItem.status !== 'pending') continue;

        try {
          const response = await apiClient.uploadFile(fileItem.file);
          results.push({
            ...fileItem,
            status: 'success',
            url: response.url,
          });
        } catch (error) {
          results.push({
            ...fileItem,
            status: 'error',
            error: error.message,
          });
        }
      }

      setUploadResults(results);
      setFiles([]);
    } catch (error) {
      console.error('Upload failed:', error);
    } finally {
      setUploading(false);
    }
  };

  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type) => {
    if (type.startsWith('image/')) return Image;
    return File;
  };

  return (
    <MainLayout title="File Upload">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-white">File Upload</h1>
          <p className="text-muted-foreground mt-1">
            Upload files for your products and content
          </p>
        </div>

        {/* Upload Zone */}
        <Card>
          <CardContent>
            <div className="border-2 border-dashed border-white/10 rounded-xl p-10 text-center cursor-pointer hover:border-primary transition">
              <input
                type="file"
                onChange={handleFileSelect}
                className="hidden"
                id="fileUpload"
                multiple
                accept="image/*,.apk,.ipa,.zip,.exe,.dmg,.pdf,.doc,.docx"
              />
              <label htmlFor="fileUpload" className="flex flex-col items-center cursor-pointer">
                <Upload className="w-12 h-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium text-white mb-1">
                  Click to upload files
                </h3>
                <p className="text-muted-foreground text-sm">
                  or drag and drop your files here
                </p>
                <p className="text-xs text-muted-foreground mt-2">
                  Supports: Images, APK, IPA, ZIP, EXE, DMG, PDF, DOC
                </p>
              </label>
            </div>
          </CardContent>
        </Card>

        {/* File List */}
        {files.length > 0 && (
          <Card>
            <CardHeader className="flex items-center justify-between">
              <CardTitle className="text-white">Files to Upload</CardTitle>
              <div className="flex items-center gap-3">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setFiles([])}
                >
                  Clear All
                </Button>
                <Button
                  onClick={uploadFiles}
                  disabled={uploading}
                  size="sm"
                  className="flex items-center gap-2"
                >
                  {uploading && <LoadingSpinner size="sm" />}
                  {uploading ? 'Uploading...' : 'Upload All'}
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              {files.map((fileItem) => {
                const FileIcon = getFileIcon(fileItem.type);
                return (
                  <div
                    key={fileItem.id}
                    className="flex items-center justify-between p-4 bg-card border border-white/5 rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      <FileIcon className="w-6 h-6 text-muted-foreground" />
                      <div>
                        <p className="text-white font-medium">{fileItem.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {formatFileSize(fileItem.size)}
                        </p>
                      </div>
                    </div>
                    <button
                      onClick={() => removeFile(fileItem.id)}
                      className="text-muted-foreground hover:text-red-400 transition-colors"
                    >
                      <Trash2 className="w-5 h-5" />
                    </button>
                  </div>
                );
              })}
            </CardContent>
          </Card>
        )}

        {/* Upload Results */}
        {uploadResults.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-white">Upload Results</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {uploadResults.map((result) => {
                const FileIcon = getFileIcon(result.type);
                return (
                  <div
                    key={result.id}
                    className="flex items-center justify-between p-4 bg-card border border-white/5 rounded-lg"
                  >
                    <div className="flex items-center space-x-3 flex-1">
                      <FileIcon className="w-6 h-6 text-muted-foreground" />
                      <div className="flex-1">
                        <p className="text-white font-medium">{result.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {formatFileSize(result.size)}
                        </p>
                        {result.status === 'success' && result.url && (
                          <div className="flex items-center gap-2 mt-2">
                            <Input
                              value={result.url}
                              readOnly
                              className="flex-1 text-xs"
                            />
                            <Button
                              size="sm"
                              variant="secondary"
                              onClick={() => copyToClipboard(result.url)}
                            >
                              <Copy className="w-4 h-4" />
                            </Button>
                          </div>
                        )}
                        {result.status === 'error' && (
                          <p className="text-sm text-red-400 mt-1">
                            {result.error}
                          </p>
                        )}
                      </div>
                    </div>
                    <div>
                      {result.status === 'success' ? (
                        <CheckCircle className="w-6 h-6 text-green-400" />
                      ) : (
                        <XCircle className="w-6 h-6 text-red-400" />
                      )}
                    </div>
                  </div>
                );
              })}

              <div className="pt-4 border-t border-white/5">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setUploadResults([])}
                >
                  Clear Results
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Upload Guidelines */}
        <Card>
          <CardHeader>
            <CardTitle className="text-white">Upload Guidelines</CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-white mb-2">File Types</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Images: PNG, JPG, JPEG, GIF</li>
                <li>• Mobile Apps: APK (Android), IPA (iOS)</li>
                <li>• Desktop Apps: EXE (Windows), DMG (macOS)</li>
                <li>• Archives: ZIP, RAR</li>
                <li>• Documents: PDF, DOC, DOCX</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-white mb-2">Size Limits</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Maximum file size: 100MB</li>
                <li>• Images: Recommended 1920x1080 or lower</li>
                <li>• App files: Under 50MB for faster downloads</li>
                <li>• Screenshots: Minimum 720p resolution</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
