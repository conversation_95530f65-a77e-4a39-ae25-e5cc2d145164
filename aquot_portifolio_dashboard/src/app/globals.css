@import url('https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;700&family=Cascadia+Code:wght@400;700&display=swap');
@import "tailwindcss";


/* Brand Colors */
:root {
  --background: #0D0D0D;
  --foreground: #FFFFFF;
  --primary: #FF9100;
  --card-bg: #171717;
  --secondary-text: #707070;
  --gradient-start: #303030;
  --gradient-end: #141414;

  /* Fonts */
  --font-primary: "Crake Test", sans-serif;
  --font-secondary: "Cascadia Code", monospace;
  --font-third: "Comfortaa", cursive;
}

/* Tailwind Theme Override */
@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-card: var(--card-bg);
  --color-secondary-text: var(--secondary-text);
  --font-sans: var(--font-primary);
  --font-mono: var(--font-secondary);
  --font-alt: var(--font-third);
}

/* Body with gradient background */
body {
  background: linear-gradient(
    135deg,
    var(--gradient-start),
    var(--gradient-end)
  );
  color: var(--foreground);
  font-family: var(--font-primary);
  min-height: 100vh;
}

/* Card (glassmorphism) */
.card {
  background: rgba(23, 23, 23, 0.6); /* glass effect */
  backdrop-filter: blur(16px) saturate(180%);
  -webkit-backdrop-filter: blur(16px) saturate(180%);
  border-radius: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.6);
  padding: 1.5rem;
  color: var(--foreground);
}

/* Text styles */
.text-primary {
  color: var(--primary);
}

.text-secondary {
  color: var(--secondary-text);
}

/* Button styles */
.btn-primary {
  background: var(--primary);
  color: var(--background);
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  transition: all 0.3s ease-in-out;
}

.btn-primary:hover {
  background: #e67f00; /* slightly darker orange */
  transform: translateY(-2px);
}
