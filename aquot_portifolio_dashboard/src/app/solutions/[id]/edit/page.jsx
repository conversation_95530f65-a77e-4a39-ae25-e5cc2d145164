'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Layout from '@/components/layouts/Layout';
import { Card, CardHeader, CardContent, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { apiService } from '@/lib/api';
import { ArrowLeft, Plus, X } from 'lucide-react';
import toast from 'react-hot-toast';
import Link from 'next/link';

const EditSolutionPage = () => {
  const params = useParams();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [formData, setFormData] = useState({
    project_name: '',
    project_smallDescription: '',
    project_description: '',
    project_image: '',
    project_link: '',
    project_client: '',
    services_provided: '',
    timeline: '',
    problem_statement: '',
    problem_images: [''],
    solution_images: ['']
  });

  const serviceTypes = [
    'Web Development',
    'Mobile App Development',
    'UI/UX Design',
    'Digital Marketing',
    'Other'
  ];

  useEffect(() => {
    if (params.id) {
      fetchSolution();
    }
  }, [params.id]);

  const fetchSolution = async () => {
    try {
      setInitialLoading(true);
      const response = await apiService.getSolutionById(params.id);
      const solution = response.data.data;
      
      setFormData({
        project_name: solution.project_name || '',
        project_smallDescription: solution.project_smallDescription || '',
        project_description: solution.project_description || '',
        project_image: solution.project_image || '',
        project_link: solution.project_link || '',
        project_client: solution.project_client || '',
        services_provided: solution.services_provided || '',
        timeline: solution.timeline || '',
        problem_statement: solution.problem_statement || '',
        problem_images: solution.problem_images?.length > 0 ? solution.problem_images : [''],
        solution_images: solution.solution_images?.length > 0 ? solution.solution_images : ['']
      });
    } catch (error) {
      console.error('Error fetching solution:', error);
      toast.error('Failed to fetch solution details');
      router.push('/solutions');
    } finally {
      setInitialLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleArrayInputChange = (field, index, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].map((item, i) => i === index ? value : item)
    }));
  };

  const addArrayField = (field) => {
    setFormData(prev => ({
      ...prev,
      [field]: [...prev[field], '']
    }));
  };

  const removeArrayField = (field, index) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Filter out empty strings from arrays
      const cleanedData = {
        ...formData,
        problem_images: formData.problem_images.filter(img => img.trim() !== ''),
        solution_images: formData.solution_images.filter(img => img.trim() !== '')
      };

      await apiService.updateSolution(params.id, cleanedData);
      toast.success('Solution updated successfully');
      router.push(`/solutions/${params.id}`);
    } catch (error) {
      console.error('Error updating solution:', error);
      toast.error('Failed to update solution');
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <Layout title="Edit Solution">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#FF9100]"></div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Edit Solution">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Link href={`/solutions/${params.id}`}>
            <Button variant="outline" size="sm">
              <ArrowLeft size={16} />
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-white">Edit Solution</h1>
            <p className="text-white/70">Update project information</p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Project Name *"
                  value={formData.project_name}
                  onChange={(e) => handleInputChange('project_name', e.target.value)}
                  placeholder="Enter project name"
                  required
                />
                <Input
                  label="Client Name *"
                  value={formData.project_client}
                  onChange={(e) => handleInputChange('project_client', e.target.value)}
                  placeholder="Enter client name"
                  required
                />
              </div>

              <Input
                label="Short Description *"
                value={formData.project_smallDescription}
                onChange={(e) => handleInputChange('project_smallDescription', e.target.value)}
                placeholder="Brief description (10-200 characters)"
                required
              />

              <div>
                <label className="block text-sm font-medium text-white/80 mb-1">
                  Project Description *
                </label>
                <textarea
                  value={formData.project_description}
                  onChange={(e) => handleInputChange('project_description', e.target.value)}
                  placeholder="Detailed project description (50-2000 characters)"
                  rows={4}
                  className="block w-full px-3 py-2 border border-white/20 bg-[#0D0D0D]/70 text-white rounded-md placeholder-white/50 focus:outline-none focus:ring-[#FF9100] focus:border-[#FF9100] sm:text-sm"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-white/80 mb-1">
                    Service Type *
                  </label>
                  <select
                    value={formData.services_provided}
                    onChange={(e) => handleInputChange('services_provided', e.target.value)}
                    className="w-full px-3 py-2 border border-white/20 bg-[#0D0D0D]/70 text-white rounded-md focus:outline-none focus:ring-[#FF9100] focus:border-[#FF9100]"
                    required
                  >
                    <option value="">Select service type</option>
                    {serviceTypes.map((service) => (
                      <option key={service} value={service}>{service}</option>
                    ))}
                  </select>
                </div>
                <Input
                  label="Timeline *"
                  value={formData.timeline}
                  onChange={(e) => handleInputChange('timeline', e.target.value)}
                  placeholder="e.g., 3 months"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Project Image URL *"
                  value={formData.project_image}
                  onChange={(e) => handleInputChange('project_image', e.target.value)}
                  placeholder="https://example.com/image.jpg"
                  type="url"
                  required
                />
                <Input
                  label="Project Link *"
                  value={formData.project_link}
                  onChange={(e) => handleInputChange('project_link', e.target.value)}
                  placeholder="https://example.com/project"
                  type="url"
                  required
                />
              </div>
            </CardContent>
          </Card>

          {/* Problem Statement */}
          <Card>
            <CardHeader>
              <CardTitle>Problem Statement</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-white/80 mb-1">
                  Problem Description *
                </label>
                <textarea
                  value={formData.problem_statement}
                  onChange={(e) => handleInputChange('problem_statement', e.target.value)}
                  placeholder="Describe the problem this project solved (20-1000 characters)"
                  rows={3}
                  className="block w-full px-3 py-2 border border-white/20 bg-[#0D0D0D]/70 text-white rounded-md placeholder-white/50 focus:outline-none focus:ring-[#FF9100] focus:border-[#FF9100] sm:text-sm"
                  required
                />
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-white/80">
                    Problem Images (Optional)
                  </label>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => addArrayField('problem_images')}
                  >
                    <Plus size={16} />
                  </Button>
                </div>
                {formData.problem_images.map((image, index) => (
                  <div key={index} className="flex gap-2 mb-2">
                    <Input
                      value={image}
                      onChange={(e) => handleArrayInputChange('problem_images', index, e.target.value)}
                      placeholder="https://example.com/problem-image.jpg"
                      type="url"
                    />
                    {formData.problem_images.length > 1 && (
                      <Button
                        type="button"
                        variant="danger"
                        size="sm"
                        onClick={() => removeArrayField('problem_images', index)}
                      >
                        <X size={16} />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Solution Images */}
          <Card>
            <CardHeader>
              <CardTitle>Solution Images</CardTitle>
            </CardHeader>
            <CardContent>
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-white/80">
                    Solution Images (Optional)
                  </label>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => addArrayField('solution_images')}
                  >
                    <Plus size={16} />
                  </Button>
                </div>
                {formData.solution_images.map((image, index) => (
                  <div key={index} className="flex gap-2 mb-2">
                    <Input
                      value={image}
                      onChange={(e) => handleArrayInputChange('solution_images', index, e.target.value)}
                      placeholder="https://example.com/solution-image.jpg"
                      type="url"
                    />
                    {formData.solution_images.length > 1 && (
                      <Button
                        type="button"
                        variant="danger"
                        size="sm"
                        onClick={() => removeArrayField('solution_images', index)}
                      >
                        <X size={16} />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end gap-4">
            <Link href={`/solutions/${params.id}`}>
              <Button variant="outline" type="button">
                Cancel
              </Button>
            </Link>
            <Button type="submit" loading={loading}>
              Update Solution
            </Button>
          </div>
        </form>
      </div>
    </Layout>
  );
};

export default EditSolutionPage;
