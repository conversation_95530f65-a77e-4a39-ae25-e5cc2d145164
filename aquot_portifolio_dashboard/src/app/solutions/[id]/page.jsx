'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Layout from '@/components/layouts/Layout';
import { Card, CardHeader, CardContent, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { apiService } from '@/lib/api';
import { formatDate } from '@/lib/utils';
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  ExternalLink,
  User,
  Calendar,
  Tag,
  Globe,
  Image as ImageIcon
} from 'lucide-react';
import toast from 'react-hot-toast';
import Link from 'next/link';

const SolutionDetailPage = () => {
  const params = useParams();
  const router = useRouter();
  const [solution, setSolution] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (params.id) {
      fetchSolution();
    }
  }, [params.id]);

  const fetchSolution = async () => {
    try {
      setLoading(true);
      const response = await apiService.getSolutionById(params.id);
      setSolution(response.data.data);
    } catch (error) {
      console.error('Error fetching solution:', error);
      toast.error('Failed to fetch solution details');
      router.push('/solutions');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this solution?')) return;
    
    try {
      await apiService.deleteSolution(params.id);
      toast.success('Solution deleted successfully');
      router.push('/solutions');
    } catch (error) {
      console.error('Error deleting solution:', error);
      toast.error('Failed to delete solution');
    }
  };

  if (loading) {
    return (
      <Layout title="Solution Details">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#FF9100]"></div>
        </div>
      </Layout>
    );
  }

  if (!solution) {
    return (
      <Layout title="Solution Not Found">
        <div className="text-center py-12">
          <p className="text-white/70 mb-4">Solution not found</p>
          <Link href="/solutions">
            <Button>Back to Solutions</Button>
          </Link>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title={solution.project_name}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/solutions">
              <Button variant="outline" size="sm">
                <ArrowLeft size={16} />
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-white">{solution.project_name}</h1>
              <p className="text-white/70">{solution.project_smallDescription}</p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Link href={`/solutions/${solution._id}/edit`}>
              <Button variant="outline">
                <Edit size={16} className="mr-2" />
                Edit
              </Button>
            </Link>
            <Button variant="danger" onClick={handleDelete}>
              <Trash2 size={16} className="mr-2" />
              Delete
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Project Image */}
            <Card>
              <CardContent className="p-0">
                <div className="aspect-video bg-gray-800 rounded-lg overflow-hidden">
                  <img
                    src={solution.project_image}
                    alt={solution.project_name}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.target.src = '/placeholder-image.jpg';
                    }}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Project Description */}
            <Card>
              <CardHeader>
                <CardTitle>Project Description</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-white/90 leading-relaxed">
                  {solution.project_description}
                </p>
              </CardContent>
            </Card>

            {/* Problem Statement */}
            <Card>
              <CardHeader>
                <CardTitle>Problem Statement</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-white/90 leading-relaxed mb-4">
                  {solution.problem_statement}
                </p>
                
                {solution.problem_images && solution.problem_images.length > 0 && (
                  <div>
                    <h4 className="text-white font-medium mb-3 flex items-center gap-2">
                      <ImageIcon size={16} />
                      Problem Images
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {solution.problem_images.map((image, index) => (
                        <div key={index} className="aspect-video bg-gray-800 rounded-lg overflow-hidden">
                          <img
                            src={image}
                            alt={`Problem ${index + 1}`}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              e.target.src = '/placeholder-image.jpg';
                            }}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Solution Images */}
            {solution.solution_images && solution.solution_images.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ImageIcon size={20} />
                    Solution Images
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {solution.solution_images.map((image, index) => (
                      <div key={index} className="aspect-video bg-gray-800 rounded-lg overflow-hidden">
                        <img
                          src={image}
                          alt={`Solution ${index + 1}`}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.target.src = '/placeholder-image.jpg';
                          }}
                        />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Project Info */}
            <Card>
              <CardHeader>
                <CardTitle>Project Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <User size={16} className="text-[#FF9100]" />
                  <div>
                    <p className="text-white/60 text-sm">Client</p>
                    <p className="text-white font-medium">{solution.project_client}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Tag size={16} className="text-[#FF9100]" />
                  <div>
                    <p className="text-white/60 text-sm">Service Type</p>
                    <p className="text-white font-medium">{solution.services_provided}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Calendar size={16} className="text-[#FF9100]" />
                  <div>
                    <p className="text-white/60 text-sm">Timeline</p>
                    <p className="text-white font-medium">{solution.timeline}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Globe size={16} className="text-[#FF9100]" />
                  <div>
                    <p className="text-white/60 text-sm">Project Link</p>
                    <a
                      href={solution.project_link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-[#FF9100] hover:text-[#e68200] transition-colors flex items-center gap-1"
                    >
                      View Project
                      <ExternalLink size={14} />
                    </a>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Timestamps */}
            <Card>
              <CardHeader>
                <CardTitle>Timestamps</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="text-white/60 text-sm">Created</p>
                  <p className="text-white font-medium">{formatDate(solution.createdAt)}</p>
                </div>
                <div>
                  <p className="text-white/60 text-sm">Last Updated</p>
                  <p className="text-white font-medium">{formatDate(solution.updatedAt)}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default SolutionDetailPage;
