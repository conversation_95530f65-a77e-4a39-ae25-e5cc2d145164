"use client";

import { useState, useEffect } from "react";
import Layout from "@/components/layouts/Layout";
import { <PERSON>, CardHeader, CardContent, CardTitle } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import { apiService } from "@/lib/api";
import { formatDate, truncateText } from "@/lib/utils";
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  ExternalLink,
  User,
  Calendar,
  Tag,
  Package,
} from "lucide-react";
import toast from "react-hot-toast";
import Link from "next/link";

const SolutionsPage = () => {
  const [solutions, setSolutions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterService, setFilterService] = useState("");
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 10,
  });

  const serviceTypes = [
    "Web Development",
    "Mobile App Development",
    "UI/UX Design",
    "Digital Marketing",
    "Other",
  ];

  const fetchSolutions = async (page = 1) => {
    try {
      setLoading(true);
      const params = {
        page,
        limit: 10,
        ...(searchTerm && { client: searchTerm }),
        ...(filterService && { services_provided: filterService }),
      };

      const response = await apiService.getSolutions(params);
      setSolutions(response.data.data);
      setPagination(response.data.pagination);
    } catch (error) {
      console.error("Error fetching solutions:", error);
      toast.error("Failed to fetch solutions");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSolutions();
  }, [searchTerm, filterService]);

  const handleDelete = async (id) => {
    if (!confirm("Are you sure you want to delete this solution?")) return;

    try {
      await apiService.deleteSolution(id);
      toast.success("Solution deleted successfully");
      fetchSolutions(pagination.currentPage);
    } catch (error) {
      console.error("Error deleting solution:", error);
      toast.error("Failed to delete solution");
    }
  };

  const handlePageChange = (page) => {
    fetchSolutions(page);
  };

  return (
    <Layout title="Solutions">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-white">
              Portfolio Solutions
            </h1>
            <p className="text-white/70">
              Manage your portfolio projects and solutions
            </p>
          </div>
          <Link href="/solutions/create">
            <Button className="flex items-center gap-2">
              <Plus size={20} />
              Add Solution
            </Button>
          </Link>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search by client name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>
              <div className="sm:w-64">
                <select
                  value={filterService}
                  onChange={(e) => setFilterService(e.target.value)}
                  className="w-full px-3 py-2 border border-white/20 bg-[#0D0D0D]/70 text-white rounded-md focus:outline-none focus:ring-[#FF9100] focus:border-[#FF9100]"
                >
                  <option value="">All Services</option>
                  {serviceTypes.map((service) => (
                    <option key={service} value={service}>
                      {service}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Solutions Grid */}
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#FF9100]"></div>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {solutions.map((solution) => (
                <Card
                  key={solution._id}
                  className="hover:shadow-md transition-shadow"
                >
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      {/* Project Image */}
                      <div className="aspect-video bg-gray-800 rounded-lg overflow-hidden">
                        <img
                          src={solution.project_image}
                          alt={solution.project_name}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.target.src = "/placeholder-image.jpg";
                          }}
                        />
                      </div>

                      {/* Project Info */}
                      <div>
                        <h3 className="font-semibold text-white text-lg mb-2">
                          {solution.project_name}
                        </h3>
                        <p className="text-white/70 text-sm mb-3">
                          {truncateText(solution.project_smallDescription, 100)}
                        </p>

                        {/* Meta Info */}
                        <div className="space-y-2 text-sm text-white/60">
                          <div className="flex items-center gap-2">
                            <User size={14} />
                            <span>{solution.project_client}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Tag size={14} />
                            <span>{solution.services_provided}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Calendar size={14} />
                            <span>{solution.timeline}</span>
                          </div>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex items-center justify-between pt-4 border-t border-white/10">
                        <div className="flex items-center gap-2">
                          <Link href={`/solutions/${solution._id}`}>
                            <Button variant="outline" size="sm">
                              <Eye size={16} />
                            </Button>
                          </Link>
                          <Link href={`/solutions/${solution._id}/edit`}>
                            <Button variant="outline" size="sm">
                              <Edit size={16} />
                            </Button>
                          </Link>
                          <Button
                            variant="danger"
                            size="sm"
                            onClick={() => handleDelete(solution._id)}
                          >
                            <Trash2 size={16} />
                          </Button>
                        </div>
                        <a
                          href={solution.project_link}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-[#FF9100] hover:text-[#e68200] transition-colors"
                        >
                          <ExternalLink size={16} />
                        </a>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <div className="flex items-center justify-center gap-2 mt-8">
                <Button
                  variant="outline"
                  disabled={!pagination.hasPrevPage}
                  onClick={() => handlePageChange(pagination.currentPage - 1)}
                >
                  Previous
                </Button>

                <span className="text-white/70 px-4">
                  Page {pagination.currentPage} of {pagination.totalPages}
                </span>

                <Button
                  variant="outline"
                  disabled={!pagination.hasNextPage}
                  onClick={() => handlePageChange(pagination.currentPage + 1)}
                >
                  Next
                </Button>
              </div>
            )}

            {/* Empty State */}
            {solutions.length === 0 && !loading && (
              <div className="text-center py-12">
                <div className="text-white/50 mb-4">
                  <Package size={48} className="mx-auto mb-4" />
                  <p>No solutions found</p>
                </div>
                <Link href="/solutions/create">
                  <Button>Create your first solution</Button>
                </Link>
              </div>
            )}
          </>
        )}
      </div>
    </Layout>
  );
};

export default SolutionsPage;
