"use client";
import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
// import logo from "@/logo_light"
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  Star,
  BarChart3,
  Upload,
  Settings,
  Menu,
  X,
  LogOut,
  ChevronDown,
  ChevronRight,
  Plus,
} from "lucide-react";
import { cn } from "@/lib/utils";

const sections = [
  {
    name: "marketplace",
    items: [
      { name: "Products", href: "/marketplace/products", icon: Package },
      { name: "Reviews", href: "/marketplace/reviews", icon: Star },
      { name: "File Upload", href: "/marketplace/upload", icon: Upload },
    ],
  },
  {
    name: "solutions",
    items: [
      { name: "All Solutions", href: "/solutions", icon: LayoutDashboard },
      { name: "Create Solution", href: "/solutions/create", icon: Plus },
    ],
  },
  {
    name: "blogs",
    items: [
      { name: "All Blogs", href: "/blogs", icon: Star },
      { name: "Create Blog", href: "/blogs/create", icon: Upload },
    ],
  },
];

const Sidebar = () => {
  const router = useRouter();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [openSections, setOpenSections] = useState({
    marketplace: true,
    solutions: true,
  });

  const toggleSection = (section) => {
    setOpenSections((prev) => ({ ...prev, [section]: !prev[section] }));
    router.push(`/${section}`);
  };

  return (
    <>
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="p-2 rounded-md bg-[#171717]/80 backdrop-blur-md text-gray-200 hover:text-white hover:bg-[#303030]"
        >
          {isMobileMenuOpen ? <X size={20} /> : <Menu size={20} />}
        </button>
      </div>

      {/* Overlay */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 z-40 bg-black/60"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-40 w-64 bg-[#0D0D0D]/70 backdrop-blur-xl border-r border-white/10 transform transition-transform duration-300 ease-in-out flex flex-col",
          isMobileMenuOpen
            ? "translate-x-0"
            : "-translate-x-full lg:translate-x-0"
        )}
      >
        {/* Logo */}
        <div className="flex items-center justify-center px-6 py-4 border-b border-white/10">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-9 bg-[#FF9100] rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">A</span>
            </div>
            <span className="text-xl font-bold text-white">Aquot Admin</span>
          </div>
        </div>

        {/* Navigation Sections */}
        <div className="flex-1 overflow-y-auto px-3 py-4 space-y-4">
          {sections.map((section) => (
            <div key={section.name}>
              {/* Section Header */}
              <button
                onClick={() => toggleSection(section.name)}
                className="flex items-center justify-between w-full text-sm font-semibold text-gray-300 px-2 py-2 rounded-md hover:bg-white/5"
              >
                <span>{section.name}</span>
                {openSections[section.name] ? (
                  <ChevronDown size={16} />
                ) : (
                  <ChevronRight size={16} />
                )}
              </button>

              {/* Section Items */}
              {openSections[section.name] && (
                <nav className="mt-1 space-y-1">
                  {section.items.map((item) => {
                    const isActive = router.pathname === item.href;
                    return (
                      <Link
                        key={item.name}
                        href={item.href}
                        className={cn(
                          "flex items-center px-3 py-2 text-sm rounded-md transition-colors",
                          isActive
                            ? "bg-[#FF9100]/20 text-[#FF9100]"
                            : "text-gray-400 hover:bg-white/5 hover:text-white"
                        )}
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        <item.icon className="mr-3 h-5 w-5" />
                        {item.name}
                      </Link>
                    );
                  })}
                </nav>
              )}
            </div>
          ))}
        </div>

        {/* Footer - Settings + Logout */}
        <div className="border-t border-white/10 p-4 space-y-2">
          <Link
            href="/settings"
            className="flex items-center px-3 py-2 text-sm text-gray-400 rounded-md hover:bg-white/5 hover:text-white"
          >
            <Settings className="mr-3 h-5 w-5" />
            Settings
          </Link>
          <button className="flex items-center w-full px-3 py-2 text-sm text-gray-400 rounded-md hover:bg-white/5 hover:text-red-400">
            <LogOut className="mr-3 h-5 w-5" />
            Logout
          </button>
          <div className="text-xs text-gray-600 text-center mt-3">
            Aquot Admin v1.0.0
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
