import { Toaster } from 'react-hot-toast';
import Sidebar from './Sidebar';
import Header from './Header';

const Layout = ({ children, title = 'Dashboard' }) => {
  return (
    <div className="flex h-screen bg-background">
      <Sidebar />
      
      <div className="flex-1 flex flex-col lg:pl-64 overflow-hidden lg:ml-0">
        <Header title={title} />
        
        <main className="flex-1 overflow-y-auto p-6">
          {children}
        </main>
      </div>
      
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
        }}
      />
    </div>
  );
};

export default Layout;