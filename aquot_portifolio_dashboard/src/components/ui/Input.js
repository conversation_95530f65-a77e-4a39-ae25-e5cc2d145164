import { cn } from '@/lib/utils';

const Input = ({ label, error, className, ...props }) => {
  return (
    <div className="space-y-1">
      {label && (
        <label className="block text-sm font-medium text-white/80">{label}</label>
      )}
      <input
        className={cn(
          'block w-full px-3 py-2 border border-white/20 bg-[#0D0D0D]/70 text-white rounded-md placeholder-white/50 focus:outline-none focus:ring-[#FF9100] focus:border-[#FF9100] sm:text-sm',
          error && 'border-red-500 focus:ring-red-500 focus:border-red-500',
          className
        )}
        {...props}
      />
      {error && <p className="text-sm text-red-500">{error}</p>}
    </div>
  );
};

export default Input;
