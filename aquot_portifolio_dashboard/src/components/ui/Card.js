import { cn } from '@/lib/utils';

const Card = ({ children, className, ...props }) => {
  return (
    <div
      className={cn(
        'bg-[#171717]/80 backdrop-blur-md rounded-lg border border-white/10 shadow-sm',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

const CardHeader = ({ children, className, ...props }) => {
  return (
    <div
      className={cn(
        'px-6 py-4 border-b border-white/10 text-white',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

const CardContent = ({ children, className, ...props }) => {
  return (
    <div className={cn('px-6 py-4 text-white/90', className)} {...props}>
      {children}
    </div>
  );
};

const CardTitle = ({ children, className, ...props }) => {
  return (
    <h3 className={cn('text-lg font-semibold text-white', className)} {...props}>
      {children}
    </h3>
  );
};

export { Card, CardHeader, CardContent, CardTitle };
