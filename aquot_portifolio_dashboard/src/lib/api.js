import axios from 'axios';
import toast from 'react-hot-toast';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const message = error.response?.data?.message || 'An error occurred';
    toast.error(message);
    return Promise.reject(error);
  }
);

// API functions
export const apiService = {
  // Products
  getProducts: (params = {}) => api.get('/marketplace/admin/products', { params }),
  createProduct: (data) => api.post('/marketplace/admin/products', data),
  updateProduct: (id, data) => api.put(`/marketplace/admin/products/${id}`, data),
  deleteProduct: (id) => api.delete(`/marketplace/admin/products/${id}`),
  
  // Public product endpoints
  getPublicProducts: (params = {}) => api.get('/marketplace/products', { params }),
  getProductById: (id) => api.get(`marketplace/products/${id}`),
  getCategories: () => api.get('marketplace/products/categories'),
  
  // File uploads
  uploadFile: (file) => {
    const formData = new FormData();
    formData.append('file', file);
    return api.post('marketplace/admin/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  
  uploadMultipleFiles: (files) => {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append('files', file);
    });
    return api.post('marketplace/admin/upload-multiple', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  
  // Analytics
  getAnalytics: (period = '30') => api.get('marketplace/admin/analytics', { params: { period } }),
  
  // Reviews
  getReportedReviews: () => api.get('marketplace/admin/reviews/reported'),
  manageReview: (id, action) => api.post(`marketplace/admin/reviews/${id}/manage`, { action }),
  
  // Transactions
  processRefund: (transactionId, data) => api.post(`marketplace/admin/transactions/${transactionId}/refund`, data),
};

export default api;