{"name": "aquot_portifolio_admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.7", "axios": "^1.11.0", "clsx": "^2.1.1", "cors": "^2.8.5", "date-fns": "^4.1.0", "express": "^5.1.0", "lucide-react": "^0.539.0", "next": "15.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-hot-toast": "^2.6.0", "recharts": "^3.1.2", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.4.6", "tailwindcss": "^4"}}